TEACHER EDUCATOR STYLE PROMPT:

Create a Social Security payment schedule script using TEACHER EDUCATOR narration style.

TONE & DELIVERY:
- Patient, clear, and methodical instruction
- Use educational techniques and structured learning
- Break down complex concepts into simple steps
- Repeat important information for retention
- Maintain encouraging, supportive teaching presence

CONTENT STRUCTURE:
1. LESSON INTRODUCTION (2-3 minutes)
   - "Welcome to today's Social Security lesson!"
   - "By the end of this video, you'll understand exactly..."
   - "Let's start with what you already know and build from there"
   - "Today's learning objectives include..."

2. FOUNDATIONAL CONCEPTS (8-10 minutes)
   - "Lesson 1: Understanding Your $1,976 Average Benefit"
   - "Let's break down how the maximum $4,873 payment is calculated"
   - "Pop Quiz: What happens when Medicare premiums are deducted?"
   - "Chapter 2: Cost-of-Living Adjustments Made Simple"
   - "Homework Review: Work Earnings and Benefit Reductions"
   - "Advanced Topic: State Supplement Programs"
   - "Final Exam Prep: Tax Implications You Need to Know"

3. PRACTICAL APPLICATION (5-7 minutes)
   - "Now let's apply what we've learned to real situations"
   - "Practice Problem: Holiday Payment Schedule Changes"
   - "Case Study: Banking and Direct Deposit Issues"
   - "Group Exercise: Understanding Birth Date Payment Systems"
   - "Review Session: Common Payment Problems and Solutions"

4. FINAL LESSON - PAYMENT DATES (3-5 minutes)
   - "For our final lesson today, let's review your payment schedule"
   - "Remember what we learned about birth date systems? Here's how it applies..."
   - "Let's practice identifying your specific payment date"
   - "Class assignment: Mark these dates on your calendar"

AUDIENCE ENGAGEMENT:
- "Can everyone follow along so far?"
- "Let's review what we just learned..."
- "Who can tell me what this means?"
- "Excellent question! Let me explain..."

WATCH TIME OPTIMIZATION:
- Use structured learning progression
- Build knowledge step by step
- Include review and repetition
- Create anticipation for "advanced lessons"
- Promise comprehensive understanding

TARGET AUDIENCE FOCUS:
- People who learn best through structured instruction
- Individuals who appreciate step-by-step guidance
- Those who want thorough understanding
- Seniors who respect educational authority
- People who need patient, repeated explanation

EMOTIONAL TRIGGERS:
- Satisfaction of learning and understanding
- Confidence through structured knowledge
- Pride in mastering complex topics
- Security through comprehensive education
- Accomplishment in completing lessons

EDUCATIONAL TECHNIQUES:
- Learning objectives and outcomes
- Progressive skill building
- Repetition and reinforcement
- Practical application exercises
- Assessment and review

TEACHING METHODS:
- Clear explanations with examples
- Visual and verbal learning support
- Step-by-step instruction
- Frequent comprehension checks
- Encouraging feedback and support

LESSON STRUCTURE:
- Introduction and objectives
- Core content delivery
- Practice and application
- Review and reinforcement
- Assessment and next steps

LANGUAGE PATTERNS:
- "Let's learn about..."
- "Can you see how..."
- "Remember when we discussed..."
- "Now that you understand..."
- "Let's practice this concept..."

EDUCATIONAL SUPPORT:
- Acknowledge different learning speeds
- Provide multiple explanations
- Offer additional resources
- Encourage questions and clarification
- Build confidence through success

CALL-TO-ACTION:
- "Subscribe for more Social Security lessons!"
- "Practice what you learned and share your success!"
- "What topic would you like to learn about next?"
- "Join our learning community for ongoing education!"

Remember: Transform Social Security information into a comprehensive educational experience that builds understanding through proven teaching methods.
