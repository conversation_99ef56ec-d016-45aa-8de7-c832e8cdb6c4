#!/usr/bin/env python3
"""
Social Security Payment Schedule Calculator
Calculates payment dates for SSI and Social Security benefits for 30 years (2025-2054)
"""

import datetime
import calendar
from typing import List, Dict, Tuple

# Federal holidays that affect payment schedules
FEDERAL_HOLIDAYS = {
    "New Year's Day": (1, 1),
    "Martin <PERSON> King Jr. Day": None,  # Third Monday in January
    "Presidents' Day": None,  # Third Monday in February  
    "Memorial Day": None,  # Last Monday in May
    "Independence Day": (7, 4),
    "Labor Day": None,  # First Monday in September
    "Columbus Day": None,  # Second Monday in October
    "Veterans Day": (11, 11),
    "Thanksgiving": None,  # Fourth Thursday in November
    "Christmas Day": (12, 25)
}

def get_federal_holidays(year: int) -> List[datetime.date]:
    """Get all federal holidays for a given year"""
    holidays = []
    
    # Fixed date holidays
    holidays.append(datetime.date(year, 1, 1))  # New Year's Day
    holidays.append(datetime.date(year, 7, 4))  # Independence Day
    holidays.append(datetime.date(year, 11, 11))  # Veterans Day
    holidays.append(datetime.date(year, 12, 25))  # Christmas Day
    
    # Variable date holidays
    # <PERSON> Jr. Day - Third Monday in January
    jan_first = datetime.date(year, 1, 1)
    days_to_first_monday = (7 - jan_first.weekday()) % 7
    first_monday_jan = jan_first + datetime.timedelta(days=days_to_first_monday)
    mlk_day = first_monday_jan + datetime.timedelta(days=14)  # Third Monday
    holidays.append(mlk_day)
    
    # Presidents' Day - Third Monday in February
    feb_first = datetime.date(year, 2, 1)
    days_to_first_monday = (7 - feb_first.weekday()) % 7
    first_monday_feb = feb_first + datetime.timedelta(days=days_to_first_monday)
    presidents_day = first_monday_feb + datetime.timedelta(days=14)  # Third Monday
    holidays.append(presidents_day)
    
    # Memorial Day - Last Monday in May
    may_last = datetime.date(year, 5, 31)
    days_back_to_monday = (may_last.weekday() + 1) % 7
    memorial_day = may_last - datetime.timedelta(days=days_back_to_monday)
    holidays.append(memorial_day)
    
    # Labor Day - First Monday in September
    sep_first = datetime.date(year, 9, 1)
    days_to_first_monday = (7 - sep_first.weekday()) % 7
    labor_day = sep_first + datetime.timedelta(days=days_to_first_monday)
    holidays.append(labor_day)
    
    # Columbus Day - Second Monday in October
    oct_first = datetime.date(year, 10, 1)
    days_to_first_monday = (7 - oct_first.weekday()) % 7
    first_monday_oct = oct_first + datetime.timedelta(days=days_to_first_monday)
    columbus_day = first_monday_oct + datetime.timedelta(days=7)  # Second Monday
    holidays.append(columbus_day)
    
    # Thanksgiving - Fourth Thursday in November
    nov_first = datetime.date(year, 11, 1)
    days_to_first_thursday = (3 - nov_first.weekday()) % 7
    first_thursday_nov = nov_first + datetime.timedelta(days=days_to_first_thursday)
    thanksgiving = first_thursday_nov + datetime.timedelta(days=21)  # Fourth Thursday
    holidays.append(thanksgiving)
    
    return sorted(holidays)

def is_business_day(date: datetime.date, holidays: List[datetime.date]) -> bool:
    """Check if a date is a business day (not weekend or holiday)"""
    return date.weekday() < 5 and date not in holidays

def get_previous_business_day(date: datetime.date, holidays: List[datetime.date]) -> datetime.date:
    """Get the previous business day before the given date"""
    prev_date = date - datetime.timedelta(days=1)
    while not is_business_day(prev_date, holidays):
        prev_date -= datetime.timedelta(days=1)
    return prev_date

def get_nth_weekday(year: int, month: int, weekday: int, n: int) -> datetime.date:
    """Get the nth occurrence of a weekday in a month (0=Monday, 6=Sunday)"""
    first_day = datetime.date(year, month, 1)
    first_weekday = first_day.weekday()
    
    # Calculate days to first occurrence of target weekday
    days_to_first = (weekday - first_weekday) % 7
    first_occurrence = first_day + datetime.timedelta(days=days_to_first)
    
    # Add weeks to get nth occurrence
    nth_occurrence = first_occurrence + datetime.timedelta(weeks=n-1)
    
    # Check if it's still in the same month
    if nth_occurrence.month != month:
        return None
    
    return nth_occurrence

def calculate_ssi_payment_date(year: int, month: int, holidays: List[datetime.date]) -> datetime.date:
    """Calculate SSI payment date (1st of month or previous business day)"""
    first_of_month = datetime.date(year, month, 1)
    
    if is_business_day(first_of_month, holidays):
        return first_of_month
    else:
        return get_previous_business_day(first_of_month, holidays)

def calculate_ss_payment_dates(year: int, month: int, holidays: List[datetime.date]) -> Dict[str, datetime.date]:
    """Calculate Social Security payment dates based on birth date ranges"""
    # Second Wednesday (birth dates 1-10)
    second_wed = get_nth_weekday(year, month, 2, 2)  # 2 = Wednesday, 2nd occurrence
    
    # Third Wednesday (birth dates 11-20)  
    third_wed = get_nth_weekday(year, month, 2, 3)   # 2 = Wednesday, 3rd occurrence
    
    # Fourth Wednesday (birth dates 21-31)
    fourth_wed = get_nth_weekday(year, month, 2, 4)  # 2 = Wednesday, 4th occurrence
    
    # Adjust for holidays
    payment_dates = {}
    
    if second_wed and is_business_day(second_wed, holidays):
        payment_dates["birth_1_10"] = second_wed
    elif second_wed:
        payment_dates["birth_1_10"] = get_previous_business_day(second_wed, holidays)
    
    if third_wed and is_business_day(third_wed, holidays):
        payment_dates["birth_11_20"] = third_wed
    elif third_wed:
        payment_dates["birth_11_20"] = get_previous_business_day(third_wed, holidays)
    
    if fourth_wed and is_business_day(fourth_wed, holidays):
        payment_dates["birth_21_31"] = fourth_wed
    elif fourth_wed:
        payment_dates["birth_21_31"] = get_previous_business_day(fourth_wed, holidays)
    
    return payment_dates

def generate_monthly_schedule(year: int, month: int) -> Dict:
    """Generate complete payment schedule for a given month"""
    holidays = get_federal_holidays(year)
    
    # SSI payment date
    ssi_date = calculate_ssi_payment_date(year, month, holidays)
    
    # Social Security payment dates
    ss_dates = calculate_ss_payment_dates(year, month, holidays)
    
    return {
        "year": year,
        "month": month,
        "month_name": calendar.month_name[month],
        "ssi_payment": ssi_date,
        "social_security_payments": ss_dates,
        "holidays_this_month": [h for h in holidays if h.month == month],
        "notes": []
    }

def generate_yearly_schedule(year: int) -> List[Dict]:
    """Generate complete payment schedule for a year"""
    yearly_schedule = []
    
    for month in range(1, 13):
        monthly_data = generate_monthly_schedule(year, month)
        yearly_schedule.append(monthly_data)
    
    return yearly_schedule

if __name__ == "__main__":
    # Test with 2025
    schedule_2025 = generate_yearly_schedule(2025)
    
    print("Social Security Payment Schedule for 2025")
    print("=" * 50)
    
    for month_data in schedule_2025:
        print(f"\n{month_data['month_name']} {month_data['year']}")
        print("-" * 30)
        print(f"SSI Payment: {month_data['ssi_payment'].strftime('%A, %B %d, %Y')}")
        
        print("\nSocial Security Payments:")
        ss_payments = month_data['social_security_payments']
        if 'birth_1_10' in ss_payments:
            print(f"  Birth dates 1-10: {ss_payments['birth_1_10'].strftime('%A, %B %d, %Y')}")
        if 'birth_11_20' in ss_payments:
            print(f"  Birth dates 11-20: {ss_payments['birth_11_20'].strftime('%A, %B %d, %Y')}")
        if 'birth_21_31' in ss_payments:
            print(f"  Birth dates 21-31: {ss_payments['birth_21_31'].strftime('%A, %B %d, %Y')}")
        
        if month_data['holidays_this_month']:
            print("\nFederal Holidays this month:")
            for holiday in month_data['holidays_this_month']:
                print(f"  {holiday.strftime('%A, %B %d, %Y')}")
